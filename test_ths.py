"""
测试同花顺软件打开脚本
"""
from open_ths_pyauto import open_tonghuashun
import pyautogui

def test_open_ths():
    """
    测试打开同花顺软件
    """
    print("开始测试同花顺软件打开...")
    print("注意：请确保同花顺软件路径正确：C:\\同花顺软件\\同花顺金融大师\\hexin.exe")
    print("如果路径不正确，请修改 open_ths_pyauto.py 中的 ths_path 变量")
    
    # 设置pyautogui安全设置
    pyautogui.FAILSAFE = True
    
    # 执行测试
    window = open_tonghuashun()
    
    if window:
        print("✓ 测试成功！同花顺软件已打开并完成操作")
        return True
    else:
        print("✗ 测试失败！")
        return False

if __name__ == "__main__":
    test_open_ths()
