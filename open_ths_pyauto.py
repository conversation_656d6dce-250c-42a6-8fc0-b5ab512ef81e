"""
使用pyautogui和pywinauto打开同花顺软件
"""
import pyautogui
import pywinauto
from pywinauto import Application
import time
import os
import subprocess

def open_tonghuashun():
    """
    打开同花顺软件并执行相关操作
    """
    # 同花顺软件路径
    ths_path = r"C:\同花顺软件\同花顺金融大师\hexin.exe"

    # 检查软件是否存在
    if not os.path.exists(ths_path):
        print(f"错误：找不到同花顺软件，路径：{ths_path}")
        return None

    try:
        print("正在启动同花顺软件...")
        # 使用subprocess启动软件
        process = subprocess.Popen(ths_path)

        # 等待软件启动
        time.sleep(5)

        # 等待应用程序完全启动
        time.sleep(8)

        # 尝试连接到应用程序
        app = None
        main_window = None
        retry_count = 0
        max_retries = 15

        while retry_count < max_retries:
            try:
                # 尝试通过进程ID连接
                app = Application().connect(process=process.pid)
                windows = app.windows()

                # 查找主窗口（通常是可见的最大窗口）
                for window in windows:
                    if window.is_visible() and window.rectangle().width() > 100:
                        main_window = window
                        break

                if main_window:
                    break

            except Exception as e:
                print(f"连接尝试 {retry_count + 1}: {str(e)}")

            # 如果通过进程ID连接失败，尝试通过窗口标题连接
            try:
                # 常见的同花顺窗口标题关键词
                possible_titles = ["同花顺", "hexin", "通达信", "金融大师"]
                for title in possible_titles:
                    try:
                        app = Application().connect(title_re=f".*{title}.*")
                        windows = app.windows()
                        if windows:
                            main_window = windows[0]
                            break
                    except:
                        continue

                if main_window:
                    break

            except Exception as e:
                print(f"标题连接尝试 {retry_count + 1}: {str(e)}")

            time.sleep(2)
            retry_count += 1
            print(f"等待窗口加载... ({retry_count}/{max_retries})")

        if main_window is None:
            print("错误：无法获取同花顺主窗口")
            print("请确保同花顺软件已正常启动")
            return None

        print(f"成功获取窗口句柄：{main_window.handle}")

        # 将窗口移动到左上角并调整大小
        print("正在调整窗口位置和大小...")
        try:
            # 先最大化窗口以确保它可见
            main_window.maximize()
            time.sleep(1)

            # 然后调整到指定大小和位置
            main_window.move_window(x=0, y=0, width=1920, height=1080)
            time.sleep(1)

            # 确保窗口在前台
            main_window.set_focus()
            main_window.restore()  # 确保窗口不是最小化状态

        except Exception as e:
            print(f"调整窗口时出错：{str(e)}")
            # 如果pywinauto调整失败，尝试使用pyautogui
            try:
                # 获取窗口位置并点击激活
                rect = main_window.rectangle()
                center_x = rect.left + rect.width() // 2
                center_y = rect.top + rect.height() // 2
                pyautogui.click(center_x, center_y)
                time.sleep(1)
            except:
                pass

        # 等待窗口稳定
        time.sleep(3)
        print("窗口已稳定")

        # 确保窗口获得焦点
        try:
            main_window.set_focus()
        except:
            # 如果设置焦点失败，尝试点击窗口
            try:
                rect = main_window.rectangle()
                center_x = rect.left + rect.width() // 2
                center_y = rect.top + rect.height() // 2
                pyautogui.click(center_x, center_y)
                time.sleep(0.5)
            except:
                pass

        # 在窗口中输入'35'
        print("正在输入'35'...")
        pyautogui.typewrite('35', interval=0.1)

        # 按回车键
        time.sleep(0.5)
        print("按下回车键...")
        pyautogui.press('enter')

        print("操作完成！")
        return main_window

    except Exception as e:
        print(f"发生错误：{str(e)}")
        return None

def main():
    """
    主函数
    """
    # 设置pyautogui的安全设置
    pyautogui.FAILSAFE = True
    pyautogui.PAUSE = 0.1

    # 打开同花顺软件
    window = open_tonghuashun()

    if window:
        print("同花顺软件已成功打开并完成操作")
        print(f"窗口句柄：{window.handle}")
    else:
        print("操作失败")

if __name__ == "__main__":
    main()